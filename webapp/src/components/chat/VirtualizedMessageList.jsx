import React, { useRef, useEffect, useState, useMemo, useCallback } from 'react';
import { useVirtualizer } from '@tanstack/react-virtual';
// Remove the react-intersection-observer import and replace with a manual implementation
// import { useInView } from 'react-intersection-observer';
import { motion, AnimatePresence } from 'framer-motion';
import { debounce } from 'lodash';
import { cn } from '../../lib/utils';

/**
 * Performance optimizations in this component:
 * 1. Uses virtualization to render only visible messages
 * 2. Implements memoization for virtual items and scroll handlers
 * 3. Uses requestAnimationFrame for smooth scrolling
 * 4. Implements efficient date grouping with caching
 * 5. Debounces scroll events for better performance
 */

// Create a simple useInView hook implementation
const useInView = (options = {}) => {
  const [ref, setRef] = useState(null);
  const [inView, setInView] = useState(false);
  
  useEffect(() => {
    if (!ref) return;
    
    const observer = new IntersectionObserver(([entry]) => {
      setInView(entry.isIntersecting);
    }, {
      threshold: options.threshold || 0,
      rootMargin: options.rootMargin || '0px',
      root: options.root || null
    });
    
    observer.observe(ref);
    
    return () => {
      observer.disconnect();
    };
  }, [ref, options.threshold, options.rootMargin, options.root]);
  
  return [inView, setRef];
};

/**
 * VirtualizedMessageList component for efficiently rendering large message lists
 * 
 * @param {Object} props
 * @param {Array} props.messages - Array of message objects to display
 * @param {Function} props.renderMessage - Function to render each message item
 * @param {boolean} props.loading - Whether more messages are being loaded
 * @param {Function} props.onLoadMore - Callback when user scrolls to top to load more
 * @param {boolean} props.hasMore - Whether there are more messages to load
 * @param {Function} props.onScrollChange - Callback when scroll position changes
 */
const VirtualizedMessageList = ({
  messages = [],
  renderMessage,
  loading = false,
  onLoadMore,
  hasMore = false,
  onScrollChange,
  className
}) => {
  const scrollContainerRef = useRef(null);
  const [lastMessageCount, setLastMessageCount] = useState(messages.length);
  const [shouldScrollToBottom, setShouldScrollToBottom] = useState(true);
  const [startReached, setStartReached] = useState(false);
  const [topInView, topInViewRef] = useInView({
    threshold: 0,
    rootMargin: '0px 0px 0px 0px'
  });
  
  // Set up virtualizer
  const virtualizer = useVirtualizer({
    count: messages.length,
    getScrollElement: () => scrollContainerRef.current,
    estimateSize: useCallback(() => 80, []), // Memoized estimate function
    overscan: 20, // Increased number of items to render outside of the visible area
    enableSmoothScroll: true, // Enable smooth scrolling for better UX
  });
  
  // Create memoized dates map for message grouping
  const messageDates = useMemo(() => {
    const dates = new Map();
    const dateCache = new Set(); // Cache for date strings to avoid repeated calculations
    
    messages.forEach((message, index) => {
      const msgDate = new Date(message.created_at).toDateString();
      
      // If this date hasn't been seen yet, or if it's the first message of the day
      if (!dateCache.has(msgDate) || 
          (index > 0 && new Date(messages[index - 1].created_at).toDateString() !== msgDate)) {
        dates.set(msgDate, index);
        dateCache.add(msgDate); // Add to date cache
      }
    });
    
    return dates;
  }, [messages]);
  
  // Detect when new messages arrive
  useEffect(() => {
    const currentCount = messages.length;
    const wasAtBottom = shouldScrollToBottom;
    
    // Check if new messages were added
    if (currentCount > lastMessageCount) {
      // Scroll to bottom only if we were already at the bottom
      if (wasAtBottom) {
        requestAnimationFrame(() => {
          scrollToBottom(true);
        });
      }
    }
    
    setLastMessageCount(currentCount);
  }, [messages.length, lastMessageCount, shouldScrollToBottom, scrollToBottom]);
  
  // Check if top of list is reached to load more messages
  useEffect(() => {
    if (topInView && hasMore && !loading && !startReached) {
      onLoadMore?.();
      setStartReached(true);
    }
  }, [topInView, hasMore, loading, onLoadMore, startReached]);
  
  // Reset startReached when new messages are loaded
  useEffect(() => {
    if (!loading && startReached) {
      setStartReached(false);
    }
  }, [loading]);
  
  // Handle scroll events to detect position
  const handleScroll = useCallback(
    debounce((e) => {
      requestAnimationFrame(() => {
        const { scrollTop, scrollHeight, clientHeight } = e.target;
        const isNearBottom = scrollHeight - scrollTop - clientHeight < 100;
        
        // Update scroll to bottom state
        setShouldScrollToBottom(isNearBottom);
        
        // Notify parent of scroll position
        onScrollChange?.({
          isNearBottom,
          scrollTop,
          scrollHeight,
          clientHeight
        });
      });
    }, 100),
    [onScrollChange]
  );
  
  // Add cleanup for debounced scroll handler
  useEffect(() => {
    return () => {
      handleScroll.cancel();
    };
  }, [handleScroll]);
  
  // Memoized scroll to bottom function for better performance
  const scrollToBottom = useCallback((smooth = false) => {
    if (scrollContainerRef.current) {
      requestAnimationFrame(() => {
        const scrollElement = scrollContainerRef.current;
        
        scrollElement.scrollTo({
          top: scrollElement.scrollHeight,
          behavior: smooth ? 'smooth' : 'auto'
        });
      });
    }
  }, []);

  // Memoize virtual items to prevent unnecessary recalculations during renders
  const virtualItems = useMemo(() => 
    virtualizer.getVirtualItems().map((virtualItem) => {
      const message = messages[virtualItem.index];
      const messageDate = new Date(message.created_at).toDateString();
      const showDateDivider = messageDates.get(messageDate) === virtualItem.index;

      return (
        <div
          key={virtualItem.key}
          className="absolute top-0 left-0 w-full"
          style={{
            height: `${virtualItem.size}px`,
            transform: `translateY(${virtualItem.start}px)`
          }}
        >
          {/* Date divider */}
          {showDateDivider && (
            <div className="flex justify-center my-2">
              <div className="px-3 py-1 bg-gray-100 rounded-full text-gray-600 text-xs font-medium">
                {new Date(message.created_at).toLocaleDateString(undefined, {
                  weekday: 'short',
                  month: 'short',
                  day: 'numeric'
                })}
              </div>
            </div>
          )}
          
          {/* Message item */}
          {renderMessage(message, virtualItem.index === messages.length - 1)}
        </div>
      );
    }),
    [virtualizer, messages, messageDates, renderMessage]
  );

  return (
    <div 
      ref={scrollContainerRef}
      onScroll={handleScroll}
      className={cn(
        "flex-1 overflow-auto relative",
        className
      )}
      data-testid="virtualized-message-list"
    >
      {/* Loading indicator */}
      <AnimatePresence>
        {loading && (
          <motion.div 
            className="sticky top-0 left-0 right-0 z-10 flex justify-center py-2 bg-white/80"
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
          >
            <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-indigo-600" />
          </motion.div>
        )}
      </AnimatePresence>
      
      {/* Observer element for detecting top of list */}
      <div ref={topInViewRef} className="h-1 w-full" />

      {/* Virtualized list */}
      <div
        className="w-full relative"
        style={{ height: `${virtualizer.getTotalSize()}px` }}
      >
        {virtualItems}
      </div>
    </div>
  );
};

// Wrap with React.memo to prevent unnecessary re-renders
export default React.memo(VirtualizedMessageList, (prevProps, nextProps) => {
  // Only re-render if messages, loading state, or hasMore changes
  return (
    prevProps.messages === nextProps.messages &&
    prevProps.loading === nextProps.loading &&
    prevProps.hasMore === nextProps.hasMore
  );
});
