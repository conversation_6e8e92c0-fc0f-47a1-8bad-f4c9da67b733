import React, { useState, useEffect } from 'react';
import { AnimatePresence } from 'framer-motion';
import OrderToast from './OrderToast';
import { useNotifications } from '../../context/NotificationContext';

const ToastContainer = ({ onAcceptOrder, onRejectOrder }) => {
    const { notifications } = useNotifications();
    const [activeToasts, setActiveToasts] = useState([]);

    // Filter for toast-worthy notifications (urgent order notifications)
    useEffect(() => {
        const urgentNotifications = notifications.filter(notification => {
            const type = notification.data?.type || '';
            // Show toasts for new orders and important status updates
            return (
                type === 'order_placed' || 
                type === 'new_order' ||
                type === 'order_accepted' ||
                type === 'order_rejected' ||
                type === 'order_completed' ||
                type === 'order_cancelled'
            ) && !notification.read;
        });

        // Update active toasts, but don't show duplicates
        setActiveToasts(prevToasts => {
            const existingIds = prevToasts.map(toast => toast.id);
            const newToasts = urgentNotifications.filter(notification => 
                !existingIds.includes(notification.id)
            );
            
            return [...prevToasts, ...newToasts];
        });
    }, [notifications]);

    const handleDismissToast = (toastId) => {
        setActiveToasts(prevToasts => 
            prevToasts.filter(toast => toast.id !== toastId)
        );
    };

    const handleAcceptOrder = async (orderId) => {
        if (onAcceptOrder) {
            await onAcceptOrder(orderId);
        }
        // Remove the toast after accepting
        setActiveToasts(prevToasts => 
            prevToasts.filter(toast => {
                const toastOrderId = toast.data?.order_id || toast.data?.orderId;
                return toastOrderId !== orderId;
            })
        );
    };

    const handleRejectOrder = async (orderId) => {
        if (onRejectOrder) {
            await onRejectOrder(orderId);
        }
        // Remove the toast after rejecting
        setActiveToasts(prevToasts => 
            prevToasts.filter(toast => {
                const toastOrderId = toast.data?.order_id || toast.data?.orderId;
                return toastOrderId !== orderId;
            })
        );
    };

    return (
        <div className="fixed top-4 right-4 z-50 space-y-4 pointer-events-none">
            <AnimatePresence>
                {activeToasts.map((notification, index) => {
                    const orderType = notification.data?.type || '';
                    const isNewOrder = orderType === 'order_placed' || orderType === 'new_order';
                    
                    return (
                        <div
                            key={notification.id}
                            className="pointer-events-auto"
                            style={{ 
                                zIndex: 1000 - index // Stack toasts properly
                            }}
                        >
                            <OrderToast
                                notification={notification}
                                onAccept={isNewOrder ? handleAcceptOrder : undefined}
                                onReject={isNewOrder ? handleRejectOrder : undefined}
                                onDismiss={() => handleDismissToast(notification.id)}
                                autoHide={!isNewOrder} // Don't auto-hide new orders
                                duration={isNewOrder ? 0 : 5000} // New orders stay until action, others auto-hide
                            />
                        </div>
                    );
                })}
            </AnimatePresence>
        </div>
    );
};

export default ToastContainer;
