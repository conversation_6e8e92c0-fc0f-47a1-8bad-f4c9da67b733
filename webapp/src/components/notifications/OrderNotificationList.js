import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import OrderNotificationItem from './OrderNotificationItem';
import { InlineLoader } from '../ui/LoadingIndicator';

const OrderNotificationList = ({ 
    notifications = [], 
    loading = false, 
    onMarkAsRead, 
    onDismiss, 
    onClearAll,
    onNotificationClick,
    maxHeight = '400px'
}) => {
    const [filter, setFilter] = useState('all'); // all, unread, orders
    const [sortBy, setSortBy] = useState('newest'); // newest, oldest

    // Filter notifications to show only order-related ones
    const orderNotifications = notifications.filter(notification => {
        const type = notification.data?.type || '';
        return type.includes('order') || notification.title?.toLowerCase().includes('order');
    });

    // Apply filters
    const filteredNotifications = orderNotifications.filter(notification => {
        switch (filter) {
            case 'unread':
                return !notification.read;
            case 'orders':
                return true; // Already filtered for orders above
            default:
                return true;
        }
    });

    // Apply sorting
    const sortedNotifications = [...filteredNotifications].sort((a, b) => {
        const dateA = new Date(a.timestamp);
        const dateB = new Date(b.timestamp);
        
        return sortBy === 'newest' ? dateB - dateA : dateA - dateB;
    });

    // Group notifications by order ID for better organization
    const groupedNotifications = sortedNotifications.reduce((groups, notification) => {
        const orderId = notification.data?.order_id || notification.data?.orderId || 'unknown';
        if (!groups[orderId]) {
            groups[orderId] = [];
        }
        groups[orderId].push(notification);
        return groups;
    }, {});

    const unreadCount = orderNotifications.filter(n => !n.read).length;

    const handleMarkAllAsRead = () => {
        orderNotifications.forEach(notification => {
            if (!notification.read) {
                onMarkAsRead(notification.id);
            }
        });
    };

    const handleClearAllOrders = () => {
        orderNotifications.forEach(notification => {
            onDismiss(notification.id);
        });
        if (onClearAll) {
            onClearAll();
        }
    };

    if (loading) {
        return (
            <div className="flex items-center justify-center py-8">
                <InlineLoader size="medium" color="indigo" />
                <span className="ml-3 text-gray-600">Loading order notifications...</span>
            </div>
        );
    }

    return (
        <div className="order-notification-list">
            {/* Header with filters and actions */}
            <div className="sticky top-0 bg-white border-b border-gray-200 p-4 z-10">
                <div className="flex items-center justify-between mb-3">
                    <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                        <svg className="w-5 h-5 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                        </svg>
                        Order Notifications
                        {unreadCount > 0 && (
                            <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                {unreadCount} new
                            </span>
                        )}
                    </h3>

                    {/* Action buttons */}
                    <div className="flex items-center space-x-2">
                        {unreadCount > 0 && (
                            <button
                                onClick={handleMarkAllAsRead}
                                className="text-sm text-indigo-600 hover:text-indigo-800 font-medium"
                            >
                                Mark all read
                            </button>
                        )}
                        {orderNotifications.length > 0 && (
                            <button
                                onClick={handleClearAllOrders}
                                className="text-sm text-gray-500 hover:text-gray-700"
                            >
                                Clear all
                            </button>
                        )}
                    </div>
                </div>

                {/* Filters */}
                <div className="flex items-center space-x-4">
                    <div className="flex items-center space-x-2">
                        <label className="text-sm text-gray-600">Filter:</label>
                        <select
                            value={filter}
                            onChange={(e) => setFilter(e.target.value)}
                            className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        >
                            <option value="all">All Orders</option>
                            <option value="unread">Unread Only</option>
                        </select>
                    </div>

                    <div className="flex items-center space-x-2">
                        <label className="text-sm text-gray-600">Sort:</label>
                        <select
                            value={sortBy}
                            onChange={(e) => setSortBy(e.target.value)}
                            className="text-sm border border-gray-300 rounded-md px-2 py-1 focus:outline-none focus:ring-2 focus:ring-indigo-500"
                        >
                            <option value="newest">Newest First</option>
                            <option value="oldest">Oldest First</option>
                        </select>
                    </div>
                </div>
            </div>

            {/* Notification list */}
            <div 
                className="overflow-y-auto"
                style={{ maxHeight }}
            >
                {sortedNotifications.length === 0 ? (
                    <div className="flex flex-col items-center justify-center py-12 px-4">
                        <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                            <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                        </div>
                        <h4 className="text-lg font-medium text-gray-900 mb-2">No order notifications</h4>
                        <p className="text-gray-500 text-center">
                            {filter === 'unread' 
                                ? "You're all caught up! No unread order notifications."
                                : "You haven't received any order notifications yet."
                            }
                        </p>
                    </div>
                ) : (
                    <AnimatePresence>
                        {sortedNotifications.map((notification) => (
                            <OrderNotificationItem
                                key={notification.id}
                                notification={notification}
                                onMarkAsRead={onMarkAsRead}
                                onDismiss={onDismiss}
                                onClick={onNotificationClick}
                            />
                        ))}
                    </AnimatePresence>
                )}
            </div>

            {/* Footer with summary */}
            {sortedNotifications.length > 0 && (
                <div className="sticky bottom-0 bg-gray-50 border-t border-gray-200 px-4 py-3">
                    <div className="flex items-center justify-between text-sm text-gray-600">
                        <span>
                            Showing {sortedNotifications.length} of {orderNotifications.length} order notifications
                        </span>
                        {unreadCount > 0 && (
                            <span className="font-medium text-indigo-600">
                                {unreadCount} unread
                            </span>
                        )}
                    </div>
                </div>
            )}
        </div>
    );
};

export default OrderNotificationList;
