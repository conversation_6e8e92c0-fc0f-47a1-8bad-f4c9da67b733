import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { useNotifications } from '../../context/NotificationContext';
import OrderNotificationList from './OrderNotificationList';
import { InlineLoader } from '../ui/LoadingIndicator';

const NotificationDropdown = ({ isOpen, onClose, onToggle }) => {
    const { 
        notifications, 
        unreadCount, 
        markAsRead, 
        markAllAsRead, 
        removeNotification, 
        clearAllNotifications 
    } = useNotifications();
    
    const [activeTab, setActiveTab] = useState('all'); // all, orders, other
    const [loading, setLoading] = useState(false);
    const dropdownRef = useRef(null);

    // Close dropdown when clicking outside
    useEffect(() => {
        const handleClickOutside = (event) => {
            if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [isOpen, onClose]);

    // Filter notifications by tab
    const getFilteredNotifications = () => {
        switch (activeTab) {
            case 'orders':
                return notifications.filter(n => {
                    const type = n.data?.type || '';
                    return type.includes('order') || n.title?.toLowerCase().includes('order');
                });
            case 'other':
                return notifications.filter(n => {
                    const type = n.data?.type || '';
                    return !type.includes('order') && !n.title?.toLowerCase().includes('order');
                });
            default:
                return notifications;
        }
    };

    const filteredNotifications = getFilteredNotifications();
    const orderNotifications = notifications.filter(n => {
        const type = n.data?.type || '';
        return type.includes('order') || n.title?.toLowerCase().includes('order');
    });
    const otherNotifications = notifications.filter(n => {
        const type = n.data?.type || '';
        return !type.includes('order') && !n.title?.toLowerCase().includes('order');
    });

    const orderUnreadCount = orderNotifications.filter(n => !n.read).length;
    const otherUnreadCount = otherNotifications.filter(n => !n.read).length;

    const handleNotificationClick = (notification) => {
        // Handle navigation based on notification type
        const type = notification.data?.type || '';
        const orderId = notification.data?.order_id || notification.data?.orderId;
        
        if (type.includes('order') && orderId) {
            // Navigate to order details
            window.location.href = `/orders/${orderId}`;
        } else if (type === 'chat_message') {
            // Navigate to chat
            window.location.href = '/chat';
        } else if (type === 'gift') {
            // Navigate to profile gifts
            window.location.href = '/profile#gifts';
        }
        
        onClose();
    };

    const handleMarkAllAsRead = () => {
        filteredNotifications.forEach(notification => {
            if (!notification.read) {
                markAsRead(notification.id);
            }
        });
    };

    const handleClearAll = () => {
        filteredNotifications.forEach(notification => {
            removeNotification(notification.id);
        });
    };

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            <motion.div
                ref={dropdownRef}
                initial={{ opacity: 0, y: -10, scale: 0.95 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -10, scale: 0.95 }}
                transition={{ duration: 0.2 }}
                className="absolute top-full right-0 mt-2 w-96 bg-white rounded-2xl shadow-2xl border border-gray-200 z-50 overflow-hidden"
                style={{ maxHeight: '80vh' }}
            >
                {/* Header */}
                <div className="bg-gradient-to-r from-indigo-50 to-blue-50 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-900 flex items-center">
                            <svg className="w-5 h-5 text-indigo-600 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                            </svg>
                            Notifications
                            {unreadCount > 0 && (
                                <span className="ml-2 inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-indigo-100 text-indigo-800">
                                    {unreadCount}
                                </span>
                            )}
                        </h3>
                        
                        <button
                            onClick={onClose}
                            className="p-1 rounded-full hover:bg-gray-200 transition-colors"
                        >
                            <svg className="w-5 h-5 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {/* Tabs */}
                    <div className="flex space-x-1 mt-3 bg-white rounded-lg p-1">
                        <button
                            onClick={() => setActiveTab('all')}
                            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                                activeTab === 'all'
                                    ? 'bg-indigo-100 text-indigo-700'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            All ({notifications.length})
                        </button>
                        <button
                            onClick={() => setActiveTab('orders')}
                            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors relative ${
                                activeTab === 'orders'
                                    ? 'bg-indigo-100 text-indigo-700'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            Orders ({orderNotifications.length})
                            {orderUnreadCount > 0 && (
                                <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                    {orderUnreadCount}
                                </span>
                            )}
                        </button>
                        <button
                            onClick={() => setActiveTab('other')}
                            className={`flex-1 px-3 py-2 text-sm font-medium rounded-md transition-colors relative ${
                                activeTab === 'other'
                                    ? 'bg-indigo-100 text-indigo-700'
                                    : 'text-gray-600 hover:text-gray-900'
                            }`}
                        >
                            Other ({otherNotifications.length})
                            {otherUnreadCount > 0 && (
                                <span className="absolute -top-1 -right-1 w-4 h-4 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                                    {otherUnreadCount}
                                </span>
                            )}
                        </button>
                    </div>
                </div>

                {/* Content */}
                <div className="max-h-96 overflow-hidden">
                    {activeTab === 'orders' ? (
                        <OrderNotificationList
                            notifications={orderNotifications}
                            loading={loading}
                            onMarkAsRead={markAsRead}
                            onDismiss={removeNotification}
                            onClearAll={handleClearAll}
                            onNotificationClick={handleNotificationClick}
                            maxHeight="384px"
                        />
                    ) : (
                        <div className="overflow-y-auto" style={{ maxHeight: '384px' }}>
                            {filteredNotifications.length === 0 ? (
                                <div className="flex flex-col items-center justify-center py-12 px-4">
                                    <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mb-4">
                                        <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                        </svg>
                                    </div>
                                    <h4 className="text-lg font-medium text-gray-900 mb-2">No notifications</h4>
                                    <p className="text-gray-500 text-center">
                                        You're all caught up! No new notifications.
                                    </p>
                                </div>
                            ) : (
                                <div>
                                    {/* Action bar for non-order tabs */}
                                    <div className="sticky top-0 bg-white border-b border-gray-200 px-4 py-3 z-10">
                                        <div className="flex items-center justify-between">
                                            <span className="text-sm text-gray-600">
                                                {filteredNotifications.length} notifications
                                            </span>
                                            <div className="flex items-center space-x-3">
                                                {filteredNotifications.some(n => !n.read) && (
                                                    <button
                                                        onClick={handleMarkAllAsRead}
                                                        className="text-sm text-indigo-600 hover:text-indigo-800 font-medium"
                                                    >
                                                        Mark all read
                                                    </button>
                                                )}
                                                <button
                                                    onClick={handleClearAll}
                                                    className="text-sm text-gray-500 hover:text-gray-700"
                                                >
                                                    Clear all
                                                </button>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Generic notification list */}
                                    <div className="divide-y divide-gray-100">
                                        {filteredNotifications.map((notification) => (
                                            <div
                                                key={notification.id}
                                                className={`p-4 hover:bg-gray-50 cursor-pointer transition-colors ${
                                                    !notification.read ? 'bg-blue-50/30' : ''
                                                }`}
                                                onClick={() => handleNotificationClick(notification)}
                                            >
                                                <div className="flex items-start space-x-3">
                                                    <div className="flex-shrink-0">
                                                        <div className="w-8 h-8 bg-indigo-100 rounded-full flex items-center justify-center">
                                                            <svg className="w-4 h-4 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                                            </svg>
                                                        </div>
                                                    </div>
                                                    <div className="flex-1 min-w-0">
                                                        <p className="text-sm font-medium text-gray-900 truncate">
                                                            {notification.title}
                                                        </p>
                                                        <p className="text-sm text-gray-600 mt-1">
                                                            {notification.body}
                                                        </p>
                                                        <p className="text-xs text-gray-500 mt-1">
                                                            {new Date(notification.timestamp).toLocaleString()}
                                                        </p>
                                                    </div>
                                                    {!notification.read && (
                                                        <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                                                    )}
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </div>
                            )}
                        </div>
                    )}
                </div>

                {/* Footer */}
                {filteredNotifications.length > 0 && (
                    <div className="bg-gray-50 px-6 py-3 border-t border-gray-200">
                        <button
                            onClick={() => {
                                window.location.href = '/notifications';
                                onClose();
                            }}
                            className="w-full text-center text-sm text-indigo-600 hover:text-indigo-800 font-medium"
                        >
                            View all notifications
                        </button>
                    </div>
                )}
            </motion.div>
        </AnimatePresence>
    );
};

export default NotificationDropdown;
