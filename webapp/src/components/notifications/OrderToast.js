import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';

const OrderToast = ({ 
    notification, 
    onAccept, 
    onReject, 
    onDismiss, 
    autoHide = true, 
    duration = 30000 // 30 seconds for order notifications
}) => {
    const [timeLeft, setTimeLeft] = useState(duration / 1000);
    const [isVisible, setIsVisible] = useState(true);
    const [isResponding, setIsResponding] = useState(false);

    const { data, title, body } = notification;
    const orderId = data?.order_id || data?.orderId;
    const orderType = data?.type || 'order';
    const customerName = data?.customer_name || data?.customerName;
    const serviceName = data?.service_name || data?.serviceName;
    const amount = data?.amount;
    const isScheduled = data?.is_scheduled || data?.isScheduled;
    const expiresAt = data?.expires_at;

    // Countdown timer for order expiration
    useEffect(() => {
        if (!expiresAt || orderType !== 'order_placed' && orderType !== 'new_order') return;

        const timer = setInterval(() => {
            const now = new Date().getTime();
            const expiry = new Date(expiresAt).getTime();
            const remaining = Math.max(0, Math.floor((expiry - now) / 1000));
            
            setTimeLeft(remaining);
            
            if (remaining <= 0) {
                setIsVisible(false);
                setTimeout(() => onDismiss(), 300);
            }
        }, 1000);

        return () => clearInterval(timer);
    }, [expiresAt, orderType, onDismiss]);

    // Auto-hide for non-urgent notifications
    useEffect(() => {
        if (autoHide && orderType !== 'order_placed' && orderType !== 'new_order') {
            const timer = setTimeout(() => {
                setIsVisible(false);
                setTimeout(() => onDismiss(), 300);
            }, duration);

            return () => clearTimeout(timer);
        }
    }, [autoHide, duration, orderType, onDismiss]);

    const handleAccept = async () => {
        setIsResponding(true);
        try {
            await onAccept(orderId);
            setIsVisible(false);
            setTimeout(() => onDismiss(), 300);
        } catch (error) {
            console.error('Error accepting order:', error);
            setIsResponding(false);
        }
    };

    const handleReject = async () => {
        setIsResponding(true);
        try {
            await onReject(orderId);
            setIsVisible(false);
            setTimeout(() => onDismiss(), 300);
        } catch (error) {
            console.error('Error rejecting order:', error);
            setIsResponding(false);
        }
    };

    const handleDismiss = () => {
        setIsVisible(false);
        setTimeout(() => onDismiss(), 300);
    };

    const formatTime = (seconds) => {
        const mins = Math.floor(seconds / 60);
        const secs = seconds % 60;
        return `${mins}:${secs.toString().padStart(2, '0')}`;
    };

    const getToastStyle = () => {
        switch (orderType) {
            case 'order_placed':
            case 'new_order':
                return {
                    bg: 'bg-gradient-to-r from-blue-500 to-indigo-600',
                    border: 'border-blue-200',
                    text: 'text-white'
                };
            case 'order_accepted':
                return {
                    bg: 'bg-gradient-to-r from-green-500 to-emerald-600',
                    border: 'border-green-200',
                    text: 'text-white'
                };
            case 'order_rejected':
                return {
                    bg: 'bg-gradient-to-r from-red-500 to-rose-600',
                    border: 'border-red-200',
                    text: 'text-white'
                };
            case 'order_completed':
                return {
                    bg: 'bg-gradient-to-r from-emerald-500 to-green-600',
                    border: 'border-emerald-200',
                    text: 'text-white'
                };
            default:
                return {
                    bg: 'bg-white',
                    border: 'border-gray-200',
                    text: 'text-gray-900'
                };
        }
    };

    const style = getToastStyle();
    const isNewOrder = orderType === 'order_placed' || orderType === 'new_order';

    if (!isVisible) return null;

    return (
        <AnimatePresence>
            <motion.div
                initial={{ opacity: 0, y: -100, scale: 0.9 }}
                animate={{ opacity: 1, y: 0, scale: 1 }}
                exit={{ opacity: 0, y: -100, scale: 0.9 }}
                transition={{ 
                    type: "spring", 
                    stiffness: 300, 
                    damping: 30 
                }}
                className={`fixed top-4 right-4 z-50 max-w-md w-full ${style.bg} ${style.border} border rounded-2xl shadow-2xl overflow-hidden`}
            >
                {/* Progress bar for countdown */}
                {isNewOrder && timeLeft > 0 && (
                    <div className="absolute top-0 left-0 h-1 bg-white/30 w-full">
                        <motion.div
                            className="h-full bg-white"
                            initial={{ width: '100%' }}
                            animate={{ width: '0%' }}
                            transition={{ duration: timeLeft, ease: 'linear' }}
                        />
                    </div>
                )}

                <div className="p-6">
                    <div className="flex items-start justify-between">
                        <div className="flex items-start space-x-4">
                            {/* Icon */}
                            <div className="flex-shrink-0">
                                {isNewOrder ? (
                                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                        </svg>
                                    </div>
                                ) : (
                                    <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                                        <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9" />
                                        </svg>
                                    </div>
                                )}
                            </div>

                            {/* Content */}
                            <div className="flex-1 min-w-0">
                                <div className="flex items-center space-x-2 mb-1">
                                    <h3 className={`text-lg font-bold ${style.text}`}>
                                        {title || `Order #${orderId}`}
                                    </h3>
                                    {isScheduled && (
                                        <span className="inline-flex px-2 py-1 text-xs font-medium bg-white/20 text-white rounded-full">
                                            Scheduled
                                        </span>
                                    )}
                                </div>
                                
                                <p className={`text-sm ${style.text} opacity-90 mb-3`}>
                                    {body || `${customerName ? `From ${customerName}` : 'New order received'}`}
                                </p>

                                {/* Order details */}
                                <div className="space-y-1">
                                    {serviceName && (
                                        <p className={`text-sm ${style.text} opacity-80`}>
                                            Service: {serviceName}
                                        </p>
                                    )}
                                    {amount && (
                                        <p className={`text-sm font-semibold ${style.text}`}>
                                            Amount: ${amount}
                                        </p>
                                    )}
                                    {isNewOrder && timeLeft > 0 && (
                                        <p className={`text-sm font-bold ${style.text} flex items-center`}>
                                            <svg className="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                            </svg>
                                            Time left: {formatTime(timeLeft)}
                                        </p>
                                    )}
                                </div>
                            </div>
                        </div>

                        {/* Close button */}
                        <button
                            onClick={handleDismiss}
                            className="flex-shrink-0 p-1 rounded-full hover:bg-white/20 transition-colors"
                        >
                            <svg className={`w-5 h-5 ${style.text}`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>

                    {/* Action buttons for new orders */}
                    {isNewOrder && onAccept && onReject && (
                        <div className="flex space-x-3 mt-4">
                            <button
                                onClick={handleAccept}
                                disabled={isResponding}
                                className="flex-1 bg-white/20 hover:bg-white/30 disabled:bg-white/10 text-white font-medium py-3 px-4 rounded-xl transition-colors disabled:cursor-not-allowed flex items-center justify-center"
                            >
                                {isResponding ? (
                                    <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                ) : (
                                    <>
                                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                        </svg>
                                        Accept
                                    </>
                                )}
                            </button>
                            <button
                                onClick={handleReject}
                                disabled={isResponding}
                                className="flex-1 bg-white/10 hover:bg-white/20 disabled:bg-white/5 text-white font-medium py-3 px-4 rounded-xl transition-colors disabled:cursor-not-allowed flex items-center justify-center"
                            >
                                {isResponding ? (
                                    <svg className="animate-spin w-5 h-5" fill="none" viewBox="0 0 24 24">
                                        <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                                        <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                    </svg>
                                ) : (
                                    <>
                                        <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                        </svg>
                                        Reject
                                    </>
                                )}
                            </button>
                        </div>
                    )}
                </div>
            </motion.div>
        </AnimatePresence>
    );
};

export default OrderToast;
