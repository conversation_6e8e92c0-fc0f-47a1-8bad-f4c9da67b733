/**
 * Edit Profile Modal Component
 *
 * A comprehensive modal for editing user profile information with:
 * - Basic profile fields (nickname, gender, height, weight, race, DOB, email)
 * - Biography editing
 * - Profile picture upload with preview
 * - Voice note upload/delete functionality
 * - Third-party access settings
 * - Real-time validation and error handling
 * - Glassmorphism styling consistent with other modals
 */

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { profileAPI } from '../../services/api';
import profileService from '../../services/profileService';
import { InlineLoader } from '../ui/LoadingIndicator';

const EditProfileModal = ({
    isOpen = false,
    onClose,
    onProfileUpdated
}) => {
    // State management
    const [loading, setLoading] = useState(true);
    const [saving, setSaving] = useState(false);
    const [errors, setErrors] = useState({});
    const [successMessage, setSuccessMessage] = useState('');
    const [races, setRaces] = useState([]);
    const [loadingRaces, setLoadingRaces] = useState(false);

    // Form data state
    const [formData, setFormData] = useState({
        nickname: '',
        gender: '',
        height: '',
        weight: '',
        race_id: '',
        date_of_birth: '',
        email: '',
        biography: '',
        profile_picture: null,
        voice_note: null,
        allow_3rd_party_access: false
    });

    // File upload states
    const [profilePicturePreview, setProfilePicturePreview] = useState(null);
    const [voiceNoteFile, setVoiceNoteFile] = useState(null);
    const [currentVoiceNote, setCurrentVoiceNote] = useState(null);

    // Cover photo states
    const [coverMediaFile, setCoverMediaFile] = useState(null);
    const [coverMediaPreview, setCoverMediaPreview] = useState(null);
    const [currentCoverMedia, setCurrentCoverMedia] = useState(null);
    const [coverMediaType, setCoverMediaType] = useState(null); // 'image' or 'video'

    // Store initial data to track changes and prevent clearing unchanged fields
    const [initialData, setInitialData] = useState({});

    // Animation variants
    const backdropVariants = {
        hidden: { opacity: 0 },
        visible: { opacity: 1 }
    };

    const modalVariants = {
        hidden: { opacity: 0, scale: 0.95, y: 20 },
        visible: { opacity: 1, scale: 1, y: 0 },
        exit: { opacity: 0, scale: 0.95, y: 20 }
    };

    // Fetch current profile data when modal opens
    useEffect(() => {
        if (isOpen) {
            fetchProfileData();
            fetchRaces();
        }
    }, [isOpen]);

    const fetchProfileData = async () => {
        setLoading(true);
        setErrors({});

        try {
            const [profileRes, bioRes] = await Promise.all([
                profileAPI.getProfile(),
                profileAPI.getBiography()
            ]);

            if (profileRes.success || profileRes.data) {
                const profileData = profileRes.data || profileRes;

                // Prepare the initial form data
                const initialFormData = {
                    nickname: profileData.nickname || '',
                    gender: profileData.gender || '',
                    height: profileData.height !== null && profileData.height !== undefined ? String(profileData.height) : '',
                    weight: profileData.weight !== null && profileData.weight !== undefined ? String(profileData.weight) : '',
                    race_id: profileData.race_id || profileData.race?.id || '',
                    date_of_birth: profileData.date_of_birth || '',
                    email: profileData.email || '',
                    biography: bioRes.data?.bio || bioRes.data?.biography || '',
                    profile_picture: null,
                    voice_note: null,
                    allow_3rd_party_access: profileData.allow_3rd_party_access || false
                };

                // Store initial data for comparison
                setInitialData({
                    nickname: profileData.nickname || '',
                    gender: profileData.gender || '',
                    height: profileData.height !== null && profileData.height !== undefined ? String(profileData.height) : '',
                    weight: profileData.weight !== null && profileData.weight !== undefined ? String(profileData.weight) : '',
                    race_id: profileData.race_id || profileData.race?.id || '',
                    date_of_birth: profileData.date_of_birth || '',
                    email: profileData.email || '',
                    biography: bioRes.data?.bio || bioRes.data?.biography || '',
                    allow_3rd_party_access: profileData.allow_3rd_party_access || false
                });

                setFormData(initialFormData);

                // Set current profile picture preview
                if (profileData.profilePicture || profileData.profile_picture) {
                    setProfilePicturePreview(profileData.profilePicture || profileData.profile_picture);
                }

                // Set current voice note
                if (profileData.voice_note) {
                    setCurrentVoiceNote(profileData.voice_note);
                }

                // Set current cover media if exists
                if (profileData.media) {
                    // Handle media structure from backend
                    let coverMedia = null;

                    if (profileData.media.photos && profileData.media.photos.length > 0) {
                        // Use the first photo as cover (or find one marked as cover)
                        coverMedia = profileData.media.photos.find(photo => photo.is_cover) || profileData.media.photos[0];
                        if (coverMedia) {
                            setCurrentCoverMedia({
                                url: `${process.env.REACT_APP_CDN_URL}/${coverMedia.path}`,
                                type: 'image',
                                order: coverMedia.order
                            });
                        }
                    } else if (profileData.media.video) {
                        // Use video as cover
                        setCurrentCoverMedia({
                            url: `${process.env.REACT_APP_CDN_URL}/${profileData.media.video}`,
                            type: 'video',
                            thumbnail: profileData.media.thumbnail ? `${process.env.REACT_APP_CDN_URL}/${profileData.media.thumbnail}` : null,
                            order: 1
                        });
                    }
                }
            }
        } catch (error) {
            console.error('Error fetching profile data:', error);
            setErrors({ general: 'Failed to load profile data. Please try again.' });
        } finally {
            setLoading(false);
        }
    };

    const fetchRaces = async () => {
        setLoadingRaces(true);
        try {
            const racesRes = await profileService.getRaces();
            if (racesRes.success) {
                setRaces(racesRes.data || []);
            }
        } catch (error) {
            console.error('Error fetching races:', error);
        } finally {
            setLoadingRaces(false);
        }
    };

    // Handle form field changes
    const handleInputChange = (field, value) => {
        setFormData(prev => ({
            ...prev,
            [field]: value
        }));

        // Clear field-specific errors
        if (errors[field]) {
            setErrors(prev => ({
                ...prev,
                [field]: null
            }));
        }
    };

    // Handle profile picture upload
    const handleProfilePictureChange = (event) => {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type and size
        const validTypes = ['image/jpeg', 'image/png', 'image/jpg', 'image/heic', 'image/heif', 'image/webp'];
        const maxSize = 20 * 1024 * 1024; // 20MB

        if (!validTypes.includes(file.type)) {
            setErrors(prev => ({
                ...prev,
                profile_picture: 'Please select a valid image file (JPEG, PNG, JPG, HEIC, HEIF, WebP)'
            }));
            return;
        }

        if (file.size > maxSize) {
            setErrors(prev => ({
                ...prev,
                profile_picture: 'Image size must be less than 20MB'
            }));
            return;
        }

        setFormData(prev => ({
            ...prev,
            profile_picture: file
        }));

        // Create preview
        const reader = new FileReader();
        reader.onload = (e) => {
            setProfilePicturePreview(e.target.result);
        };
        reader.readAsDataURL(file);

        // Clear errors
        setErrors(prev => ({
            ...prev,
            profile_picture: null
        }));
    };

    // Handle voice note upload
    const handleVoiceNoteChange = (event) => {
        const file = event.target.files[0];
        if (!file) return;

        // Validate file type and size
        const validTypes = ['audio/mp3', 'audio/wav', 'audio/m4a', 'audio/aac', 'audio/ogg'];
        const maxSize = 5 * 1024 * 1024; // 5MB

        if (!validTypes.includes(file.type)) {
            setErrors(prev => ({
                ...prev,
                voice_note: 'Please select a valid audio file (MP3, WAV, M4A, AAC, OGG)'
            }));
            return;
        }

        if (file.size > maxSize) {
            setErrors(prev => ({
                ...prev,
                voice_note: 'Audio file size must be less than 5MB'
            }));
            return;
        }

        setVoiceNoteFile(file);
        setFormData(prev => ({
            ...prev,
            voice_note: file
        }));

        // Clear errors
        setErrors(prev => ({
            ...prev,
            voice_note: null
        }));
    };

    // Handle cover media file selection
    const handleCoverMediaChange = (e) => {
        const file = e.target.files[0];
        if (!file) return;

        console.log('=== COVER MEDIA VALIDATION ===');
        console.log('File name:', file.name);
        console.log('File type:', file.type);
        console.log('File size:', file.size);

        // Validate file type with specific formats
        const isImage = file.type.startsWith('image/');
        const isVideo = file.type.startsWith('video/');

        // Check specific image formats
        const allowedImageTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/heic', 'image/heif', 'image/webp'];
        const allowedVideoTypes = ['video/mp4', 'video/mov', 'video/avi', 'video/flv', 'video/wmv', 'video/3gp', 'video/mkv'];

        if (!isImage && !isVideo) {
            setErrors(prev => ({
                ...prev,
                cover_media: 'Please select a valid image or video file'
            }));
            return;
        }

        if (isImage && !allowedImageTypes.includes(file.type)) {
            setErrors(prev => ({
                ...prev,
                cover_media: 'Image must be JPEG, PNG, HEIC, HEIF, or WebP format'
            }));
            return;
        }

        if (isVideo && !allowedVideoTypes.includes(file.type)) {
            setErrors(prev => ({
                ...prev,
                cover_media: 'Video must be MP4, MOV, AVI, FLV, WMV, 3GP, or MKV format'
            }));
            return;
        }

        // Validate file size
        const maxSize = isVideo ? 50 * 1024 * 1024 : 20 * 1024 * 1024; // 50MB for video, 20MB for image
        if (file.size > maxSize) {
            setErrors(prev => ({
                ...prev,
                cover_media: `File size must be less than ${isVideo ? '50MB' : '20MB'}`
            }));
            return;
        }

        // For videos, perform comprehensive FFmpeg compatibility validation
        if (isVideo) {
            console.log('🎥 Starting comprehensive video validation...');

            // Use our enhanced validation functions
            profileService.validateVideoDuration(file).then(async (durationValidation) => {
                if (!durationValidation.isValid) {
                    console.error('❌ Video duration validation failed:', durationValidation.errors);
                    setErrors(prev => ({
                        ...prev,
                        cover_media: durationValidation.errors.join('\n')
                    }));
                    return;
                }

                // Check FFmpeg compatibility
                const compatibilityCheck = await profileService.checkFFmpegCompatibility(file);
                if (!compatibilityCheck.compatible) {
                    console.error('❌ Video FFmpeg compatibility check failed:', compatibilityCheck.issues);
                    const errorMessage = `❌ Video Compatibility Issues:\n\n${compatibilityCheck.issues.join('\n')}\n\n💡 ${compatibilityCheck.recommendation}\n\n🔧 Try this FFmpeg command:\nffmpeg -i input.mp4 -c:v libx264 -c:a aac -vf "scale=1280:720" -r 30 -t 8 output.mp4`;
                    setErrors(prev => ({
                        ...prev,
                        cover_media: errorMessage
                    }));
                    return;
                }

                // Show warnings if any
                if (durationValidation.warnings && durationValidation.warnings.length > 0) {
                    console.warn('⚠️ Video warnings:', durationValidation.warnings);
                }

                console.log('✅ Video passed all compatibility checks:', {
                    metadata: durationValidation.metadata,
                    compatible: compatibilityCheck.compatible
                });

                // If all validations pass, proceed with file processing
                processValidFile(file, isVideo);
            }).catch((error) => {
                console.error('❌ Video validation error:', error);
                setErrors(prev => ({
                    ...prev,
                    cover_media: 'Failed to validate video file. Please try a different video.'
                }));
            });
        } else {
            // For images, process immediately
            processValidFile(file, isVideo);
        }

        function processValidFile(file, isVideo) {
            // Clear any previous errors
            setErrors(prev => {
                const newErrors = { ...prev };
                delete newErrors.cover_media;
                return newErrors;
            });

            // Set the media type explicitly
            const mediaType = isVideo ? 'video' : 'image';
            
            setCoverMediaFile(file);
            setCoverMediaType(mediaType);

            // Create preview URL
            const reader = new FileReader();
            reader.onloadend = () => {
                setCoverMediaPreview(reader.result);
            };
            reader.readAsDataURL(file);

            console.log('File validation passed:', {
                name: file.name,
                type: file.type,
                size: file.size,
                mediaType: mediaType
            });
            
            // Log the state update for debugging
            console.log('Setting cover media type to:', mediaType);
        }

        console.log('==============================');
    };



    // Handle cover media deletion
    const handleDeleteCoverMedia = async (mediaOrder) => {
        try {
            const result = await profileService.deleteMedia(mediaOrder);

            if (result.success) {
                setCurrentCoverMedia(null);
                setSuccessMessage('Cover media deleted successfully');
                setTimeout(() => setSuccessMessage(''), 3000);
            } else {
                throw new Error(result.error || 'Failed to delete cover media');
            }
        } catch (error) {
            console.error('Error deleting cover media:', error);
            setErrors(prev => ({
                ...prev,
                cover_media: 'Failed to delete cover media. Please try again.'
            }));
        }
    };

    // Handle voice note deletion
    const handleDeleteVoiceNote = async () => {
        try {
            const result = await profileService.deleteVoiceNote();

            if (result.success) {
                setCurrentVoiceNote(null);
                setVoiceNoteFile(null);
                setFormData(prev => ({
                    ...prev,
                    voice_note: null
                }));
                setSuccessMessage('Voice note deleted successfully');
                setTimeout(() => setSuccessMessage(''), 3000);
            } else {
                throw new Error(result.error || 'Failed to delete voice note');
            }
        } catch (error) {
            console.error('Error deleting voice note:', error);
            setErrors(prev => ({
                ...prev,
                voice_note: 'Failed to delete voice note. Please try again.'
            }));
        }
    };

    // Form validation
    const validateForm = () => {
        const newErrors = {};

        if (formData.nickname && formData.nickname.length > 20) {
            newErrors.nickname = 'Nickname must be 20 characters or less';
        }

        if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            newErrors.email = 'Please enter a valid email address';
        }

        if (formData.height && formData.height.toString().trim() !== '') {
            const heightValue = parseFloat(formData.height);
            if (isNaN(heightValue) || heightValue < 0) {
                newErrors.height = 'Please enter a valid height (positive number)';
            }
        }

        if (formData.weight && formData.weight.toString().trim() !== '') {
            const weightValue = parseFloat(formData.weight);
            if (isNaN(weightValue) || weightValue < 0) {
                newErrors.weight = 'Please enter a valid weight (positive number)';
            }
        }

        if (formData.biography && formData.biography.length > 1000) {
            newErrors.biography = 'Biography must be 1000 characters or less';
        }

        setErrors(newErrors);
        return Object.keys(newErrors).length === 0;
    };

    // Handle form submission
    const handleSubmit = async (e) => {
        e.preventDefault();

        if (!validateForm()) {
            return;
        }

        setSaving(true);
        setErrors({});
        setSuccessMessage('');

        try {
            console.log('=== EDIT PROFILE MODAL DEBUG ===');
            console.log('Initial data:', initialData);
            console.log('Current form data:', formData);

            // Step 1: Upload cover media first if there's a new one
            if (coverMediaFile && !currentCoverMedia) {
                console.log('Uploading cover media first...');
                setSuccessMessage('Uploading cover media...');

                // Ensure coverMediaType is properly set before upload
                const mediaType = coverMediaType || (coverMediaFile.type.startsWith('image/') ? 'image' : 'video');
                console.log('Cover media type for upload:', mediaType);

                try {
                    const coverResult = await profileService.uploadMedia(coverMediaFile, mediaType);
                    if (coverResult.success) {
                        setCurrentCoverMedia(coverResult.data);
                        console.log('Cover media uploaded successfully:', coverResult.data);
                        setSuccessMessage('Cover media uploaded successfully! Updating profile...');
                    } else {
                        console.error('Cover media upload failed:', coverResult.error);

                        // For video upload failures, provide detailed error message
                        if (mediaType === 'video') {
                            setErrors({
                                general: coverResult.error || 'Video upload failed. Please check the video format and try again.'
                            });
                        } else {
                            setErrors({
                                general: coverResult.error || 'Failed to upload cover media'
                            });
                        }

                        throw new Error(coverResult.error || 'Failed to upload cover media');
                    }
                } catch (uploadError) {
                    console.error('Cover media upload exception:', uploadError);
                    setErrors({
                        general: uploadError.message || 'An unexpected error occurred during media upload'
                    });
                    throw uploadError;
                }
            }

            // Step 2: Prepare profile data - only include changed fields or preserve existing values
            const profileData = {};

            // Always include all fields to prevent clearing unchanged ones
            profileData.nickname = formData.nickname || initialData.nickname || '';
            profileData.gender = formData.gender || initialData.gender || '';
            profileData.race_id = formData.race_id || initialData.race_id || '';
            profileData.date_of_birth = formData.date_of_birth || initialData.date_of_birth || '';
            profileData.email = formData.email || initialData.email || '';
            profileData.biography = formData.biography !== undefined ? formData.biography : (initialData.biography || '');
            profileData.allow_3rd_party_access = formData.allow_3rd_party_access !== undefined ? formData.allow_3rd_party_access : (initialData.allow_3rd_party_access || false);

            // Handle height and weight - preserve existing values if not changed
            if (formData.height !== undefined && formData.height !== '') {
                profileData.height = formData.height;
            } else if (initialData.height !== undefined && initialData.height !== '') {
                profileData.height = initialData.height;
            }

            if (formData.weight !== undefined && formData.weight !== '') {
                profileData.weight = formData.weight;
            } else if (initialData.weight !== undefined && initialData.weight !== '') {
                profileData.weight = initialData.weight;
            }

            // Include files if they exist
            if (formData.profile_picture) {
                profileData.profile_picture = formData.profile_picture;
            }
            if (formData.voice_note) {
                profileData.voice_note = formData.voice_note;
            }

            console.log('Final profile data to submit:', profileData);
            console.log('================================');

            // Step 3: Update profile using profileService
            const result = await profileService.updateProfile(profileData);

            if (!result.success) {
                // Handle validation errors
                if (result.validationErrors) {
                    const backendErrors = {};
                    Object.keys(result.validationErrors).forEach(field => {
                        backendErrors[field] = result.validationErrors[field][0]; // Take first error message
                    });
                    setErrors(backendErrors);
                    return;
                }

                throw new Error(result.error || 'Failed to update profile');
            }

            console.log('Profile update result:', result);

            setSuccessMessage('Profile updated successfully!');

            // Step 4: Notify parent component with updated data
            if (onProfileUpdated) {
                console.log('Calling onProfileUpdated with data:', result.data);
                onProfileUpdated(result.data);
            }

            // Close modal after a short delay
            setTimeout(() => {
                onClose();
                setSuccessMessage('');
            }, 2000);

        } catch (error) {
            console.error('Error updating profile:', error);
            setErrors({ general: error.message || 'Failed to update profile. Please try again.' });
        } finally {
            setSaving(false);
        }
    };

    // Handle ESC key press
    useEffect(() => {
        const handleEscKey = (event) => {
            if (event.key === 'Escape' && isOpen) {
                onClose();
            }
        };

        if (isOpen) {
            document.addEventListener('keydown', handleEscKey);
            document.body.style.overflow = 'hidden';
        }

        return () => {
            document.removeEventListener('keydown', handleEscKey);
            document.body.style.overflow = 'unset';
        };
    }, [isOpen, onClose]);

    if (!isOpen) return null;

    return (
        <AnimatePresence>
            <motion.div
                className="fixed inset-0 z-50 flex items-center justify-center p-4"
                variants={backdropVariants}
                initial="hidden"
                animate="visible"
                exit="hidden"
                onClick={(e) => {
                    if (e.target === e.currentTarget) {
                        onClose();
                    }
                }}
            >
                {/* Backdrop */}
                <div className="absolute inset-0 bg-black/50 backdrop-blur-sm" />

                {/* Modal Content */}
                <motion.div
                    className="relative w-full max-w-4xl max-h-[90vh] overflow-hidden"
                    variants={modalVariants}
                    initial="hidden"
                    animate="visible"
                    exit="exit"
                    transition={{ type: "spring", damping: 25, stiffness: 300 }}
                >
                    {/* Glassmorphism Container */}
                    <div className="relative bg-gradient-to-br from-white/95 to-white/85 backdrop-blur-2xl border border-white/30 rounded-3xl shadow-2xl overflow-hidden">
                        {/* Animated background decorations */}
                        <div className="absolute -top-10 -right-10 w-32 h-32 bg-gradient-to-br from-indigo-400/20 to-purple-400/20 rounded-full blur-2xl animate-pulse" />
                        <div className="absolute -bottom-10 -left-10 w-24 h-24 bg-gradient-to-br from-blue-400/20 to-indigo-400/20 rounded-full blur-xl animate-pulse delay-1000" />

                        {/* Header */}
                        <div className="relative z-10 flex items-center justify-between p-6 border-b border-white/20">
                            <div className="flex items-center space-x-3">
                                <div className="p-3 bg-gradient-to-br from-indigo-500 to-purple-600 rounded-2xl shadow-lg">
                                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                    </svg>
                                </div>
                                <h2 className="text-2xl font-bold bg-gradient-to-r from-indigo-700 to-purple-700 bg-clip-text text-transparent">
                                    Edit Profile
                                </h2>
                            </div>
                            <motion.button
                                onClick={onClose}
                                className="p-2 text-gray-500 hover:text-gray-700 hover:bg-white/20 rounded-xl transition-all duration-200"
                                whileHover={{ scale: 1.1 }}
                                whileTap={{ scale: 0.9 }}
                            >
                                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                </svg>
                            </motion.button>
                        </div>

                        {/* Content */}
                        <div className="relative z-10 p-6 overflow-y-auto max-h-[calc(90vh-8rem)]">
                            {loading ? (
                                <div className="flex items-center justify-center py-12">
                                    <InlineLoader size="large" color="indigo" />
                                    <span className="ml-3 text-gray-600">Loading profile data...</span>
                                </div>
                            ) : (
                                <form onSubmit={handleSubmit} className="max-w-3xl space-y-8">
                                    {/* Success Message */}
                                    {successMessage && (
                                        <motion.div
                                            className="p-4 bg-green-50 border border-green-200 rounded-xl"
                                            initial={{ opacity: 0, y: -10 }}
                                            animate={{ opacity: 1, y: 0 }}
                                        >
                                            <div className="flex items-center">
                                                <svg className="w-5 h-5 text-green-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                                                </svg>
                                                <span className="text-green-700 font-medium">{successMessage}</span>
                                            </div>
                                        </motion.div>
                                    )}

                                    {/* General Error */}
                                    {errors.general && (
                                        <motion.div
                                            className="p-4 bg-red-50 border border-red-200 rounded-xl"
                                            initial={{ opacity: 0, y: -10 }}
                                            animate={{ opacity: 1, y: 0 }}
                                        >
                                            <div className="flex items-center">
                                                <svg className="w-5 h-5 text-red-500 mr-2" fill="currentColor" viewBox="0 0 20 20">
                                                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clipRule="evenodd" />
                                                </svg>
                                                <span className="text-red-700 font-medium">{errors.general}</span>
                                            </div>
                                        </motion.div>
                                    )}

                                    {/* Cover Photo Section */}
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold text-gray-800">Cover Photo</h3>

                                        {/* Current Cover Media Display */}
                                        {currentCoverMedia && !coverMediaPreview && (
                                            <div className="relative">
                                                <div className="w-full h-48 rounded-2xl overflow-hidden border-4 border-white shadow-lg bg-gray-100">
                                                    {currentCoverMedia.type === 'video' ? (
                                                        <video
                                                            src={currentCoverMedia.url}
                                                            className="w-full h-full object-cover"
                                                            controls
                                                            muted
                                                        />
                                                    ) : (
                                                        <img
                                                            src={currentCoverMedia.url}
                                                            alt="Current cover"
                                                            className="w-full h-full object-cover"
                                                        />
                                                    )}
                                                </div>
                                                <button
                                                    type="button"
                                                    onClick={() => handleDeleteCoverMedia(currentCoverMedia.order)}
                                                    className="absolute top-3 right-3 p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors shadow-lg"
                                                >
                                                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1-1H8a1 1 0 00-1 1v3M4 7h16" />
                                                    </svg>
                                                </button>
                                            </div>
                                        )}

                                        {/* Cover Media Preview */}
                                        {coverMediaPreview && (
                                            <div className="relative">
                                                <div className="w-full h-48 rounded-2xl overflow-hidden border-4 border-indigo-200 shadow-lg bg-gray-100">
                                                    {coverMediaType === 'video' ? (
                                                        <video
                                                            src={coverMediaPreview}
                                                            className="w-full h-full object-cover"
                                                            controls
                                                            muted
                                                        />
                                                    ) : (
                                                        <img
                                                            src={coverMediaPreview}
                                                            alt="Cover preview"
                                                            className="w-full h-full object-cover"
                                                        />
                                                    )}
                                                </div>
                                                <div className="absolute top-3 right-3 flex space-x-2">
                                                    <div className="p-2 bg-blue-500 text-white rounded-full shadow-lg">
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                        </svg>
                                                    </div>
                                                    <button
                                                        type="button"
                                                        onClick={() => {
                                                            setCoverMediaFile(null);
                                                            setCoverMediaPreview(null);
                                                            setCoverMediaType(null);
                                                        }}
                                                        className="p-2 bg-red-500 text-white rounded-full hover:bg-red-600 transition-colors shadow-lg"
                                                    >
                                                        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
                                                        </svg>
                                                    </button>
                                                </div>
                                                <div className="absolute bottom-3 left-3 bg-black/50 text-white px-3 py-1 rounded-full text-sm">
                                                    Will upload when you save
                                                </div>
                                            </div>
                                        )}



                                        {/* Upload Area */}
                                        {!currentCoverMedia && !coverMediaPreview && (
                                            <div className="relative">
                                                <input
                                                    type="file"
                                                    id="cover_media"
                                                    accept="image/jpeg,image/png,image/jpg,image/heic,image/heif,image/webp,video/mp4,video/mov,video/avi,video/flv,video/wmv,video/3gp,video/mkv"
                                                    onChange={handleCoverMediaChange}
                                                    className="hidden"
                                                />
                                                <label
                                                    htmlFor="cover_media"
                                                    className="flex flex-col items-center justify-center w-full h-48 border-2 border-dashed border-gray-300 rounded-2xl cursor-pointer bg-gradient-to-br from-gray-50 to-gray-100 hover:from-indigo-50 hover:to-purple-50 hover:border-indigo-300 transition-all duration-300"
                                                >
                                                    <div className="flex flex-col items-center justify-center pt-5 pb-6">
                                                        <svg className="w-12 h-12 mb-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                                        </svg>
                                                        <p className="mb-2 text-sm text-gray-500">
                                                            <span className="font-semibold">Click to upload</span> or drag and drop
                                                        </p>
                                                        <p className="text-xs text-gray-500">
                                                            Images: JPEG, PNG, WebP (max 20MB)
                                                        </p>
                                                        <p className="text-xs text-gray-500">
                                                            Videos: MP4 recommended (max 50MB, 8 seconds)
                                                        </p>
                                                        <p className="text-xs text-indigo-600 font-medium">
                                                            ⚡ Best: 1280x720, H.264/AAC, even dimensions
                                                        </p>
                                                    </div>
                                                </label>
                                            </div>
                                        )}

                                        {errors.cover_media && (
                                            <p className="text-sm text-red-600">{errors.cover_media}</p>
                                        )}
                                    </div>

                                    {/* Profile Picture Section */}
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold text-gray-800">Profile Picture</h3>
                                        <div className="flex items-center space-x-6">
                                            <div className="relative">
                                                <div className="w-24 h-24 rounded-full overflow-hidden border-4 border-white shadow-lg bg-gray-100">
                                                    {profilePicturePreview ? (
                                                        <img
                                                            src={profilePicturePreview}
                                                            alt="Profile preview"
                                                            className="w-full h-full object-cover"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full flex items-center justify-center bg-gradient-to-br from-gray-200 to-gray-300">
                                                            <svg className="w-8 h-8 text-gray-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                                                            </svg>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>
                                            <div className="flex-1">
                                                <input
                                                    type="file"
                                                    id="profile_picture"
                                                    accept="image/jpeg,image/png,image/jpg,image/heic,image/heif,image/webp"
                                                    onChange={handleProfilePictureChange}
                                                    className="hidden"
                                                />
                                                <label
                                                    htmlFor="profile_picture"
                                                    className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 cursor-pointer"
                                                >
                                                    <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M15 13l-3-3m0 0l-3 3m3-3v12" />
                                                    </svg>
                                                    Upload New Picture
                                                </label>
                                                <p className="text-sm text-gray-500 mt-2">
                                                    JPEG, PNG, JPG, HEIC, HEIF, WebP (max 20MB)
                                                </p>
                                                {errors.profile_picture && (
                                                    <p className="text-sm text-red-600 mt-1">{errors.profile_picture}</p>
                                                )}
                                            </div>
                                        </div>
                                    </div>

                                    {/* Basic Information Section */}
                                    <div className="space-y-6">
                                        <h3 className="text-lg font-semibold text-gray-800">Basic Information</h3>

                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                            {/* Nickname */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Nickname
                                                </label>
                                                <input
                                                    type="text"
                                                    value={formData.nickname}
                                                    onChange={(e) => handleInputChange('nickname', e.target.value)}
                                                    maxLength={20}
                                                    className="w-full px-4 py-3 bg-white/80 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                                    placeholder="Enter your nickname"
                                                />
                                                <div className="flex justify-between items-center mt-1">
                                                    {errors.nickname && (
                                                        <p className="text-sm text-red-600">{errors.nickname}</p>
                                                    )}
                                                    <p className="text-xs text-gray-500 ml-auto">
                                                        {formData.nickname.length}/20
                                                    </p>
                                                </div>
                                            </div>

                                            {/* Gender */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Gender
                                                </label>
                                                <select
                                                    value={formData.gender}
                                                    onChange={(e) => handleInputChange('gender', e.target.value)}
                                                    className="w-full px-4 py-3 bg-white/80 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                                >
                                                    <option value="">Select gender</option>
                                                    <option value="Male">Male</option>
                                                    <option value="Female">Female</option>
                                                </select>
                                                {errors.gender && (
                                                    <p className="text-sm text-red-600 mt-1">{errors.gender}</p>
                                                )}
                                            </div>

                                            {/* Date of Birth */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Date of Birth
                                                </label>
                                                <input
                                                    type="date"
                                                    value={formData.date_of_birth}
                                                    onChange={(e) => handleInputChange('date_of_birth', e.target.value)}
                                                    className="w-full px-4 py-3 bg-white/80 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                                />
                                                {errors.date_of_birth && (
                                                    <p className="text-sm text-red-600 mt-1">{errors.date_of_birth}</p>
                                                )}
                                            </div>

                                            {/* Race */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Race
                                                </label>
                                                <select
                                                    value={formData.race_id}
                                                    onChange={(e) => handleInputChange('race_id', e.target.value)}
                                                    className="w-full px-4 py-3 bg-white/80 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                                    disabled={loadingRaces}
                                                >
                                                    <option value="">Select race</option>
                                                    {races.map((race) => (
                                                        <option key={race.id} value={race.id}>
                                                            {race.name}
                                                        </option>
                                                    ))}
                                                </select>
                                                {loadingRaces && (
                                                    <p className="text-sm text-gray-500 mt-1">Loading races...</p>
                                                )}
                                                {errors.race_id && (
                                                    <p className="text-sm text-red-600 mt-1">{errors.race_id}</p>
                                                )}
                                            </div>

                                            {/* Height */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Height (cm)
                                                </label>
                                                <input
                                                    type="number"
                                                    value={formData.height}
                                                    onChange={(e) => handleInputChange('height', e.target.value)}
                                                    min="0"
                                                    step="0.1"
                                                    className="w-full px-4 py-3 bg-white/80 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                                    placeholder="Enter height in cm"
                                                />
                                                {errors.height && (
                                                    <p className="text-sm text-red-600 mt-1">{errors.height}</p>
                                                )}
                                            </div>

                                            {/* Weight */}
                                            <div>
                                                <label className="block text-sm font-medium text-gray-700 mb-2">
                                                    Weight (kg)
                                                </label>
                                                <input
                                                    type="number"
                                                    value={formData.weight}
                                                    onChange={(e) => handleInputChange('weight', e.target.value)}
                                                    min="0"
                                                    step="0.1"
                                                    className="w-full px-4 py-3 bg-white/80 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                                    placeholder="Enter weight in kg"
                                                />
                                                {errors.weight && (
                                                    <p className="text-sm text-red-600 mt-1">{errors.weight}</p>
                                                )}
                                            </div>
                                        </div>

                                        {/* Email */}
                                        <div>
                                            <label className="block text-sm font-medium text-gray-700 mb-2">
                                                Email Address
                                            </label>
                                            <input
                                                type="email"
                                                value={formData.email}
                                                onChange={(e) => handleInputChange('email', e.target.value)}
                                                className="w-full px-4 py-3 bg-white/80 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200"
                                                placeholder="Enter your email address"
                                            />
                                            {errors.email && (
                                                <p className="text-sm text-red-600 mt-1">{errors.email}</p>
                                            )}
                                        </div>
                                    </div>

                                    {/* Biography Section */}
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold text-gray-800">Biography</h3>
                                        <div>
                                            <textarea
                                                value={formData.biography}
                                                onChange={(e) => handleInputChange('biography', e.target.value)}
                                                maxLength={1000}
                                                rows={4}
                                                className="w-full px-4 py-3 bg-white/80 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:border-transparent transition-all duration-200 resize-none"
                                                placeholder="Tell us about yourself..."
                                            />
                                            <div className="flex justify-between items-center mt-1">
                                                {errors.biography && (
                                                    <p className="text-sm text-red-600">{errors.biography}</p>
                                                )}
                                                <p className="text-xs text-gray-500 ml-auto">
                                                    {formData.biography.length}/1000
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                    {/* Voice Note Section */}
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold text-gray-800">Voice Note</h3>

                                        {currentVoiceNote && !voiceNoteFile && (
                                            <div className="p-4 bg-blue-50 border border-blue-200 rounded-xl">
                                                <div className="flex items-center justify-between">
                                                    <div className="flex items-center space-x-3">
                                                        <svg className="w-8 h-8 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                                                        </svg>
                                                        <div>
                                                            <p className="text-sm font-medium text-blue-800">Current voice note</p>
                                                            <audio controls className="mt-2">
                                                                <source src={currentVoiceNote} type="audio/mpeg" />
                                                                Your browser does not support the audio element.
                                                            </audio>
                                                        </div>
                                                    </div>
                                                    <button
                                                        type="button"
                                                        onClick={handleDeleteVoiceNote}
                                                        className="px-3 py-1 bg-red-500 text-white text-sm rounded-lg hover:bg-red-600 transition-colors"
                                                    >
                                                        Delete
                                                    </button>
                                                </div>
                                            </div>
                                        )}

                                        <div>
                                            <input
                                                type="file"
                                                id="voice_note"
                                                accept="audio/mp3,audio/wav,audio/m4a,audio/aac,audio/ogg"
                                                onChange={handleVoiceNoteChange}
                                                className="hidden"
                                            />
                                            <label
                                                htmlFor="voice_note"
                                                className="inline-flex items-center px-4 py-2 bg-gradient-to-r from-green-500 to-emerald-600 text-white rounded-xl hover:from-green-600 hover:to-emerald-700 transition-all duration-300 cursor-pointer"
                                            >
                                                <svg className="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M19 11a7 7 0 01-7 7m0 0a7 7 0 01-7-7m7 7v4m0 0H8m4 0h4m-4-8a3 3 0 01-3-3V5a3 3 0 116 0v6a3 3 0 01-3 3z" />
                                                </svg>
                                                {currentVoiceNote ? 'Replace Voice Note' : 'Upload Voice Note'}
                                            </label>
                                            <p className="text-sm text-gray-500 mt-2">
                                                MP3, WAV, M4A, AAC, OGG (max 5MB, 15 seconds)
                                            </p>
                                            {errors.voice_note && (
                                                <p className="text-sm text-red-600 mt-1">{errors.voice_note}</p>
                                            )}
                                        </div>

                                        {voiceNoteFile && (
                                            <div className="p-4 bg-green-50 border border-green-200 rounded-xl">
                                                <div className="flex items-center space-x-3">
                                                    <svg className="w-8 h-8 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                                    </svg>
                                                    <div>
                                                        <p className="text-sm font-medium text-green-800">New voice note ready</p>
                                                        <p className="text-xs text-green-600">{voiceNoteFile.name}</p>
                                                    </div>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* Third-party Access Section */}
                                    <div className="space-y-4">
                                        <h3 className="text-lg font-semibold text-gray-800">Privacy Settings</h3>
                                        <div className="flex items-center justify-between p-4 bg-gray-50 rounded-xl">
                                            <div>
                                                <p className="font-medium text-left text-gray-800">Allow third-party access</p>
                                                <p className="text-sm text-gray-600">Allow external services to access your profile data</p>
                                            </div>
                                            <label className="relative inline-flex items-center cursor-pointer">
                                                <input
                                                    type="checkbox"
                                                    checked={formData.allow_3rd_party_access}
                                                    onChange={(e) => handleInputChange('allow_3rd_party_access', e.target.checked)}
                                                    className="sr-only peer"
                                                />
                                                <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-indigo-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-indigo-600"></div>
                                            </label>
                                        </div>
                                    </div>

                                    {/* Submit Button */}
                                    <div className="flex justify-end space-x-4 pt-6 border-t border-gray-200">
                                        <motion.button
                                            type="button"
                                            onClick={onClose}
                                            className="px-6 py-3 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-xl transition-all duration-200"
                                            whileHover={{ scale: 1.02 }}
                                            whileTap={{ scale: 0.98 }}
                                        >
                                            Cancel
                                        </motion.button>
                                        <motion.button
                                            type="submit"
                                            disabled={saving}
                                            className="px-8 py-3 bg-gradient-to-r from-indigo-500 to-purple-600 text-white rounded-xl hover:from-indigo-600 hover:to-purple-700 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center space-x-2"
                                            whileHover={{ scale: saving ? 1 : 1.02 }}
                                            whileTap={{ scale: saving ? 1 : 0.98 }}
                                        >
                                            {saving ? (
                                                <>
                                                    <InlineLoader size="small" color="white" />
                                                    <span>Saving...</span>
                                                </>
                                            ) : (
                                                <>
                                                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
                                                    </svg>
                                                    <span>Save Changes</span>
                                                </>
                                            )}
                                        </motion.button>
                                    </div>
                                </form>
                            )}
                        </div>
                    </div>
                </motion.div>
            </motion.div>
        </AnimatePresence>
    );
};

export default EditProfileModal;
